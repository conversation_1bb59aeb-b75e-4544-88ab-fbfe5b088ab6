{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:datasources_v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"dataSources": {"methods": {"create": {"description": "Creates the new data source configuration for the given account. This method always creates a new data source.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources", "httpMethod": "POST", "id": "merchantapi.accounts.dataSources.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The account where this data source will be created. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "datasources/v1beta/{+parent}/dataSources", "request": {"$ref": "DataSource"}, "response": {"$ref": "DataSource"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "delete": {"description": "Deletes a data source from your Merchant Center account.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources/{dataSourcesId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.dataSources.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the data source to delete. Format: `accounts/{account}/dataSources/{datasource}`", "location": "path", "pattern": "^accounts/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "datasources/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "fetch": {"description": "Performs the data fetch immediately (even outside fetch schedule) on a data source from your Merchant Center Account. If you need to call this method more than once per day, you should use the Products service to update your product data instead. This method only works on data sources with a file input set.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources/{dataSourcesId}:fetch", "httpMethod": "POST", "id": "merchantapi.accounts.dataSources.fetch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the data source resource to fetch. Format: `accounts/{account}/dataSources/{datasource}`", "location": "path", "pattern": "^accounts/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "datasources/v1beta/{+name}:fetch", "request": {"$ref": "FetchDataSourceRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves the data source configuration for the given account.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources/{dataSourcesId}", "httpMethod": "GET", "id": "merchantapi.accounts.dataSources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the data source to retrieve. Format: `accounts/{account}/dataSources/{datasource}`", "location": "path", "pattern": "^accounts/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}}, "path": "datasources/v1beta/{+name}", "response": {"$ref": "DataSource"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the configurations for data sources for the given account.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources", "httpMethod": "GET", "id": "merchantapi.accounts.dataSources.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of data sources to return. The service may return fewer than this value. The maximum value is 1000; values above 1000 will be coerced to 1000. If unspecified, the maximum number of data sources will be returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDataSources` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListDataSources` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account to list data sources for. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "datasources/v1beta/{+parent}/dataSources", "response": {"$ref": "ListDataSourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "patch": {"description": "Updates the existing data source configuration. The fields that are set in the update mask but not provided in the resource will be deleted.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources/{dataSourcesId}", "httpMethod": "PATCH", "id": "merchantapi.accounts.dataSources.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Identifier. The name of the data source. Format: `accounts/{account}/dataSources/{datasource}`", "location": "path", "pattern": "^accounts/[^/]+/dataSources/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Required. The list of data source fields to be updated. Fields specified in the update mask without a value specified in the body will be deleted from the data source. Providing special \"*\" value for full data source replacement is not supported. For example, If you insert `updateMask=displayName` in the request, it will only update the `displayName` leaving all other fields untouched.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "datasources/v1beta/{+name}", "request": {"$ref": "DataSource"}, "response": {"$ref": "DataSource"}, "scopes": ["https://www.googleapis.com/auth/content"]}}, "resources": {"fileUploads": {"methods": {"get": {"description": "Gets the latest data source file upload. Only the `latest` alias is accepted for a file upload.", "flatPath": "datasources/v1beta/accounts/{accountsId}/dataSources/{dataSourcesId}/fileUploads/{fileUploadsId}", "httpMethod": "GET", "id": "merchantapi.accounts.dataSources.fileUploads.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the data source file upload to retrieve. Format: `accounts/{account}/dataSources/{datasource}/fileUploads/latest`", "location": "path", "pattern": "^accounts/[^/]+/dataSources/[^/]+/fileUploads/[^/]+$", "required": true, "type": "string"}}, "path": "datasources/v1beta/{+name}", "response": {"$ref": "FileUpload"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"DataSource": {"description": "The [data source](/merchant/api/guides/data-sources/overview) for the Merchant Center account.", "id": "DataSource", "properties": {"dataSourceId": {"description": "Output only. The data source id.", "format": "int64", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. The displayed data source name in the Merchant Center UI.", "type": "string"}, "fileInput": {"$ref": "FileInput", "description": "Optional. The field is used only when data is managed through a file."}, "input": {"description": "Output only. Determines the type of input to the data source. Based on the input some settings might not work. Only generic data sources can be created through the API.", "enum": ["INPUT_UNSPECIFIED", "API", "FILE", "UI", "AUTOFEED"], "enumDescriptions": ["Input unspecified.", "Represents data sources for which the data is primarily provided through the API.", "Represents data sources for which the data is primarily provided through file input. Data can still be provided through the API.", "The data source for products added directly in Merchant Center. This type of data source can not be created or updated through this API, only by Merchant Center UI. This type of data source is read only.", "This is also known as [Automated feeds](https://support.google.com/merchants/answer/********) used to automatically build your product data. This type of data source can be enabled or disabled through the Accounts sub-API."], "readOnly": true, "type": "string"}, "localInventoryDataSource": {"$ref": "LocalInventoryDataSource", "description": "The [local inventory](https://support.google.com/merchants/answer/7023001) data source."}, "merchantReviewDataSource": {"$ref": "MerchantReviewDataSource", "description": "The [merchant review](https://support.google.com/merchants/answer/7045996) data source."}, "name": {"description": "Required. Identifier. The name of the data source. Format: `accounts/{account}/dataSources/{datasource}`", "type": "string"}, "primaryProductDataSource": {"$ref": "PrimaryProductDataSource", "description": "The [primary data source](https://support.google.com/merchants/answer/7439058) for local and online products."}, "productReviewDataSource": {"$ref": "ProductReviewDataSource", "description": "The [product review](https://support.google.com/merchants/answer/7045996) data source."}, "promotionDataSource": {"$ref": "PromotionDataSource", "description": "The [promotion](https://support.google.com/merchants/answer/2906014) data source."}, "regionalInventoryDataSource": {"$ref": "RegionalInventoryDataSource", "description": "The [regional inventory](https://support.google.com/merchants/answer/7439058) data source."}, "supplementalProductDataSource": {"$ref": "SupplementalProductDataSource", "description": "The [supplemental data source](https://support.google.com/merchants/answer/7439058) for local and online products."}}, "type": "object"}, "DataSourceReference": {"description": "Data source reference can be used to manage related data sources within the data source service.", "id": "DataSourceReference", "properties": {"primaryDataSourceName": {"description": "Optional. The name of the primary data source. Format: `accounts/{account}/dataSources/{datasource}`", "type": "string"}, "self": {"description": "Self should be used to reference the primary data source itself.", "type": "boolean"}, "supplementalDataSourceName": {"description": "Optional. The name of the supplemental data source. Format: `accounts/{account}/dataSources/{datasource}`", "type": "string"}}, "type": "object"}, "DefaultRule": {"description": "Default rule management of the data source.", "id": "DefaultRule", "properties": {"takeFromDataSources": {"description": "Required. The list of data sources linked in the [default rule](https://support.google.com/merchants/answer/7450276). This list is ordered by the default rule priority of joining the data. It might include none or multiple references to `self` and supplemental data sources. The list must not be empty. To link the data source to the default rule, you need to add a new reference to this list (in sequential order). To unlink the data source from the default rule, you need to remove the given reference from this list. Changing the order of this list will result in changing the priority of data sources in the default rule. For example, providing the following list: [`1001`, `self`] will take attribute values from supplemental data source `1001`, and fallback to `self` if the attribute is not set in `1001`.", "items": {"$ref": "DataSourceReference"}, "type": "array"}}, "type": "object"}, "Destination": {"description": "Destinations also known as [Marketing methods](https://support.google.com/merchants/answer/15130232) selections.", "id": "Destination", "properties": {"destination": {"description": "[Marketing methods](https://support.google.com/merchants/answer/15130232) (also known as destination) selections.", "enum": ["DESTINATION_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "YOUTUBE_SHOPPING", "YOUTUBE_SHOPPING_CHECKOUT", "YOUTUBE_AFFILIATE", "FREE_VEHICLE_LISTINGS", "VEHICLE_ADS", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL"], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/google-ads/answer/2454022).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3057972).", "[Free listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[YouTube Shopping](https://support.google.com/merchants/answer/12362804).", "Youtube shopping checkout.", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[Free vehicle listings](https://support.google.com/merchants/answer/11189169).", "[Vehicle ads](https://support.google.com/merchants/answer/11189169).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail)."], "type": "string"}, "state": {"description": "The state of the destination.", "enum": ["STATE_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["Not specified.", "Indicates that the destination is enabled.", "Indicates that the destination is disabled."], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "FetchDataSourceRequest": {"description": "Request message for the FetchDataSource method.", "id": "FetchDataSourceRequest", "properties": {}, "type": "object"}, "FetchSettings": {"description": "Fetch details to deliver the data source.", "id": "FetchSettings", "properties": {"dayOfMonth": {"description": "Optional. The day of the month when the data source file should be fetched (1-31). This field can only be set for monthly frequency.", "format": "int32", "type": "integer"}, "dayOfWeek": {"description": "Optional. The day of the week when the data source file should be fetched. This field can only be set for weekly frequency.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["The day of the week is unspecified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "enabled": {"description": "Optional. Enables or pauses the fetch schedule.", "type": "boolean"}, "fetchUri": {"description": "Optional. The URL where the data source file can be fetched. Google Merchant Center supports automatic scheduled uploads using the HTTP, HTTPS or SFTP protocols, so the value will need to be a valid link using one of those three protocols. Immutable for Google Sheets files.", "type": "string"}, "frequency": {"description": "Required. The frequency describing fetch schedule.", "enum": ["FREQUENCY_UNSPECIFIED", "FREQUENCY_DAILY", "FREQUENCY_WEEKLY", "FREQUENCY_MONTHLY"], "enumDescriptions": ["Frequency unspecified.", "The fetch happens every day.", "The fetch happens every week.", "The fetch happens every month."], "type": "string"}, "password": {"description": "Optional. An optional password for fetch_uri. Used for [submitting data sources through SFTP](https://support.google.com/merchants/answer/13813117).", "type": "string"}, "timeOfDay": {"$ref": "TimeOfDay", "description": "Optional. The hour of the day when the data source file should be fetched. Minutes and seconds are not supported and will be ignored."}, "timeZone": {"description": "Optional. [Time zone](https://cldr.unicode.org) used for schedule. UTC by default. For example, \"America/Los_Angeles\".", "type": "string"}, "username": {"description": "Optional. An optional user name for fetch_uri. Used for [submitting data sources through SFTP](https://support.google.com/merchants/answer/13813117).", "type": "string"}}, "type": "object"}, "FileInput": {"description": "The data specific for file data sources. This field is empty for other data source inputs.", "id": "FileInput", "properties": {"fetchSettings": {"$ref": "FetchSettings", "description": "Optional. Fetch details to deliver the data source. It contains settings for `FETCH` and `GOOGLE_SHEETS` file input types. The required fields vary based on the frequency of fetching."}, "fileInputType": {"description": "Output only. The type of file input.", "enum": ["FILE_INPUT_TYPE_UNSPECIFIED", "UPLOAD", "FETCH", "GOOGLE_SHEETS"], "enumDescriptions": ["File input type unspecified.", "The file is uploaded through SFTP, Google Cloud Storage or manually in the Merchant Center.", "The file is fetched from the configured fetch_uri.", "The file is fetched from Google Sheets specified in the fetch_uri."], "readOnly": true, "type": "string"}, "fileName": {"description": "Optional. The file name of the data source. Required for `UPLOAD` file input type.", "type": "string"}}, "type": "object"}, "FileUpload": {"description": "The file upload of a specific data source, that is, the result of the retrieval of the data source at a certain timestamp computed asynchronously when the data source processing is finished. Only applicable to file data sources.", "id": "FileUpload", "properties": {"dataSourceId": {"description": "Output only. The data source id.", "format": "int64", "readOnly": true, "type": "string"}, "issues": {"description": "Output only. The list of issues occurring in the data source.", "items": {"$ref": "Issue"}, "readOnly": true, "type": "array"}, "itemsCreated": {"description": "Output only. The number of items in the data source that were created.", "format": "int64", "readOnly": true, "type": "string"}, "itemsTotal": {"description": "Output only. The number of items in the data source that were processed.", "format": "int64", "readOnly": true, "type": "string"}, "itemsUpdated": {"description": "Output only. The number of items in the data source that were updated.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the data source file upload. Format: `{datasource.name=accounts/{account}/dataSources/{datasource}/fileUploads/{fileupload}}`", "type": "string"}, "processingState": {"description": "Output only. The processing state of the data source.", "enum": ["PROCESSING_STATE_UNSPECIFIED", "FAILED", "IN_PROGRESS", "SUCCEEDED"], "enumDescriptions": ["Processing state unspecified.", "The data source could not be processed or all the items had errors.", "The data source is being processed.", "The data source was processed successfully, though some items might have had errors."], "readOnly": true, "type": "string"}, "uploadTime": {"description": "Output only. The date at which the file of the data source was uploaded.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "Issue": {"description": "An error occurring in the data source, like \"invalid price\".", "id": "Issue", "properties": {"code": {"description": "Output only. The code of the error, for example, \"validation/invalid_value\". Returns \"?\" if the code is unknown.", "readOnly": true, "type": "string"}, "count": {"description": "Output only. The number of occurrences of the error in the file upload.", "format": "int64", "readOnly": true, "type": "string"}, "description": {"description": "Output only. The error description, for example, \"Your data source contains items which have too many attributes, or are too big. These items will be dropped\".", "readOnly": true, "type": "string"}, "documentationUri": {"description": "Output only. Link to the documentation explaining the issue in more details, if available.", "readOnly": true, "type": "string"}, "severity": {"description": "Output only. The severity of the issue.", "enum": ["SEVERITY_UNSPECIFIED", "WARNING", "ERROR"], "enumDescriptions": ["Severity unspecified.", "The issue is the warning.", "The issue is an error."], "readOnly": true, "type": "string"}, "title": {"description": "Output only. The title of the issue, for example, \"Item too big\".", "readOnly": true, "type": "string"}}, "type": "object"}, "ListDataSourcesResponse": {"description": "Response message for the ListDataSources method.", "id": "ListDataSourcesResponse", "properties": {"dataSources": {"description": "The data sources from the specified account.", "items": {"$ref": "DataSource"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "LocalInventoryDataSource": {"description": "The local inventory data source.", "id": "LocalInventoryDataSource", "properties": {"contentLanguage": {"description": "Required. Immutable. The two-letter ISO 639-1 language of the items to which the local inventory is provided.", "type": "string"}, "feedLabel": {"description": "Required. Immutable. The feed label of the offers to which the local inventory is provided. Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9), and dashes (-). See also [migration to feed labels](https://developers.google.com/shopping-content/guides/products/feed-labels).", "type": "string"}}, "type": "object"}, "MerchantReviewDataSource": {"description": "The merchant review data source.", "id": "MerchantReviewDataSource", "properties": {}, "type": "object"}, "PrimaryProductDataSource": {"description": "The primary data source for local and online products.", "id": "PrimaryProductDataSource", "properties": {"channel": {"description": "Optional. Immutable. Specifies the type of data source channel.", "enum": ["CHANNEL_UNSPECIFIED", "ONLINE_PRODUCTS", "LOCAL_PRODUCTS", "PRODUCTS"], "enumDescriptions": ["Not specified.", "Online product.", "Local product.", "Unified data source for both local and online products. Note: Products management through the API is not possible for this channel."], "type": "string"}, "contentLanguage": {"description": "Optional. Immutable. The two-letter ISO 639-1 language of the items in the data source. `feedLabel` and `contentLanguage` must be either both set or unset. The fields can only be unset for data sources without file input. If set, the data source will only accept products matching this combination. If unset, the data source will accept products without that restriction.", "type": "string"}, "countries": {"description": "Optional. The countries where the items may be displayed. Represented as a [CLDR territory code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml).", "items": {"type": "string"}, "type": "array"}, "defaultRule": {"$ref": "DefaultRule", "description": "Optional. Default rule management of the data source. If set, the linked data sources will be replaced."}, "destinations": {"description": "Optional. A list of destinations describing where products of the data source can be shown. When retrieving the data source, the list contains all the destinations that can be used for the data source, including the ones that are disabled for the data source but enabled for the account. Only destinations that are enabled on the account, for example through program participation, can be enabled on the data source. If unset, during creation, the destinations will be inherited based on the account level program participation. If set, during creation or update, the data source will be set only for the specified destinations. Updating this field requires at least one destination.", "items": {"$ref": "Destination"}, "type": "array"}, "feedLabel": {"description": "Optional. Immutable. The feed label that is specified on the data source level. Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9), and dashes (-). See also [migration to feed labels](https://developers.google.com/shopping-content/guides/products/feed-labels). `feedLabel` and `contentLanguage` must be either both set or unset for data sources with product content type. They must be set for data sources with a file input. If set, the data source will only accept products matching this combination. If unset, the data source will accept products without that restriction.", "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductReviewDataSource": {"description": "The product review data source.", "id": "ProductReviewDataSource", "properties": {}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not be set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}, "PromotionDataSource": {"description": "The promotion data source.", "id": "PromotionDataSource", "properties": {"contentLanguage": {"description": "Required. Immutable. The two-letter ISO 639-1 language of the items in the data source.", "type": "string"}, "targetCountry": {"description": "Required. Immutable. The target country used as part of the unique identifier. Represented as a [CLDR territory code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml). Promotions are only available in selected [countries](https://support.google.com/merchants/answer/4588460).", "type": "string"}}, "type": "object"}, "RegionalInventoryDataSource": {"description": "The regional inventory data source.", "id": "RegionalInventoryDataSource", "properties": {"contentLanguage": {"description": "Required. Immutable. The two-letter ISO 639-1 language of the items to which the regional inventory is provided.", "type": "string"}, "feedLabel": {"description": "Required. Immutable. The feed label of the offers to which the regional inventory is provided. Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9), and dashes (-). See also [migration to feed labels](https://developers.google.com/shopping-content/guides/products/feed-labels).", "type": "string"}}, "type": "object"}, "SupplementalProductDataSource": {"description": "The supplemental data source for local and online products. After creation, you should make sure to link the supplemental product data source into one or more primary product data sources.", "id": "SupplementalProductDataSource", "properties": {"contentLanguage": {"description": "Optional. Immutable. The two-letter ISO 639-1 language of the items in the data source. `feedLabel` and `contentLanguage` must be either both set or unset. The fields can only be unset for data sources without file input. If set, the data source will only accept products matching this combination. If unset, the data source will accept produts without that restriction.", "type": "string"}, "feedLabel": {"description": "Optional. Immutable. The feed label that is specified on the data source level. Must be less than or equal to 20 uppercase letters (A-Z), numbers (0-9), and dashes (-). See also [migration to feed labels](https://developers.google.com/shopping-content/guides/products/feed-labels). `feedLabel` and `contentLanguage` must be either both set or unset for data sources with product content type. They must be set for data sources with a file input. The fields must be unset for data sources without file input. If set, the data source will only accept products matching this combination. If unset, the data source will accept produts without that restriction.", "type": "string"}, "referencingPrimaryDataSources": {"description": "Output only. The (unordered and deduplicated) list of all primary data sources linked to this data source in either default or custom rules. Supplemental data source cannot be deleted before all links are removed.", "items": {"$ref": "DataSourceReference"}, "readOnly": true, "type": "array"}}, "type": "object"}, "TimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "TimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "datasources_v1beta", "version_module": true}