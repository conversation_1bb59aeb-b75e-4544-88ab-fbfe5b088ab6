"""Global hotkey listener for triggering screen capture."""

import logging
from typing import Callable, Optional
from pynput import keyboard
from pynput.keyboard import Key, KeyCode, Listener
import threading
import time

logger = logging.getLogger(__name__)


class HotkeyListener:
    """Listens for global hotkey combinations and triggers callbacks."""
    
    def __init__(self, hotkey_combination: str = "ctrl+print_screen"):
        """
        Initialize the hotkey listener.
        
        Args:
            hotkey_combination: String representation of hotkey (e.g., "ctrl+print_screen")
        """
        self.hotkey_combination = hotkey_combination
        self.callback: Optional[Callable] = None
        self.listener: Optional[Listener] = None
        self.is_running = False
        self._parse_hotkey()
        
    def _parse_hotkey(self) -> None:
        """Parse the hotkey combination string into key objects."""
        parts = self.hotkey_combination.lower().split('+')
        self.modifier_keys = set()
        self.trigger_key = None
        
        for part in parts:
            part = part.strip()
            if part == "ctrl":
                self.modifier_keys.add(Key.ctrl_l)
                self.modifier_keys.add(Key.ctrl_r)
            elif part == "alt":
                self.modifier_keys.add(Key.alt_l)
                self.modifier_keys.add(Key.alt_r)
            elif part == "shift":
                self.modifier_keys.add(Key.shift_l)
                self.modifier_keys.add(Key.shift_r)
            elif part == "print_screen":
                self.trigger_key = Key.print_screen
            elif part == "f1":
                self.trigger_key = Key.f1
            elif part == "f2":
                self.trigger_key = Key.f2
            # Add more keys as needed
            else:
                # Handle single character keys
                if len(part) == 1:
                    self.trigger_key = KeyCode.from_char(part)
                    
        logger.info(f"Parsed hotkey: modifiers={self.modifier_keys}, trigger={self.trigger_key}")
    
    def set_callback(self, callback: Callable) -> None:
        """Set the callback function to be called when hotkey is pressed."""
        self.callback = callback
        logger.info("Hotkey callback set")
    
    def _on_key_press(self, key) -> None:
        """Handle key press events."""
        try:
            # Check if this is our trigger key
            if key == self.trigger_key:
                # Check if all required modifier keys are currently pressed
                current_modifiers = set()
                
                # Get currently pressed keys from the listener
                if hasattr(self.listener, '_pressed_keys'):
                    pressed_keys = self.listener._pressed_keys
                    for pressed_key in pressed_keys:
                        if pressed_key in self.modifier_keys:
                            current_modifiers.add(pressed_key)
                
                # Check if any of the required modifier key pairs are pressed
                required_modifiers_satisfied = True
                if Key.ctrl_l in self.modifier_keys or Key.ctrl_r in self.modifier_keys:
                    if not (Key.ctrl_l in current_modifiers or Key.ctrl_r in current_modifiers):
                        required_modifiers_satisfied = False
                
                if Key.alt_l in self.modifier_keys or Key.alt_r in self.modifier_keys:
                    if not (Key.alt_l in current_modifiers or Key.alt_r in current_modifiers):
                        required_modifiers_satisfied = False
                        
                if Key.shift_l in self.modifier_keys or Key.shift_r in self.modifier_keys:
                    if not (Key.shift_l in current_modifiers or Key.shift_r in current_modifiers):
                        required_modifiers_satisfied = False
                
                if required_modifiers_satisfied and self.callback:
                    logger.info(f"Hotkey {self.hotkey_combination} triggered")
                    # Run callback in a separate thread to avoid blocking the listener
                    threading.Thread(target=self.callback, daemon=True).start()
                    
        except Exception as e:
            logger.error(f"Error in key press handler: {e}")
    
    def _on_key_release(self, key) -> None:
        """Handle key release events."""
        pass
    
    def start(self) -> None:
        """Start listening for hotkey events."""
        if self.is_running:
            logger.warning("Hotkey listener is already running")
            return
            
        if not self.callback:
            raise ValueError("No callback function set. Use set_callback() first.")
        
        logger.info(f"Starting hotkey listener for: {self.hotkey_combination}")
        
        # Use a global hotkey approach with pynput
        try:
            logger.info(f"Using global hotkey listener with combo: {self.hotkey_combination}")
            with keyboard.GlobalHotKeys({
                self.hotkey_combination: self.callback
            }) as hotkey:
                self.is_running = True
                logger.info("Hotkey listener started successfully")
                hotkey.join()
        except Exception as e:
            logger.error(f"Failed to start global hotkey listener: {e}")
            # Fallback to regular key listener
            self._start_fallback_listener()
    
    def _start_fallback_listener(self) -> None:
        """Fallback listener implementation."""
        logger.info("Using fallback key listener")
        self.listener = Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )
        self.listener.start()
        self.is_running = True
        
        # Keep the listener running
        try:
            while self.is_running:
                time.sleep(0.1)
        except KeyboardInterrupt:
            self.stop()
    
    def stop(self) -> None:
        """Stop the hotkey listener."""
        if not self.is_running:
            return
            
        logger.info("Stopping hotkey listener")
        self.is_running = False
        
        if self.listener:
            self.listener.stop()
            self.listener = None
            
        logger.info("Hotkey listener stopped")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()
